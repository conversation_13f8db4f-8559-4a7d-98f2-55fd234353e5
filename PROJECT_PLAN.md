# Coqui TTS Optimize Projesi

## Proje Bilgileri
- <PERSON><PERSON>: Optimize Coqui TTS
- Versiyon: 1.0.0
- Geliştirme Ortamı: Local
- Hedef Platform: Windows 10
- Sistem Gereksinimleri: Minimum 8GB RAM, x64 işlemci

## <PERSON><PERSON><PERSON><PERSON> Teknolojiler
- Coqui TTS: 0.21.3 (Optimize edilmiş sürüm)
- Python: 3.10+
- PyQt5: 5.15.9 (GUI için)
- PyTorch: 2.1.0
- TorchAudio: 2.1.0

## Klasör Yapısı
```
project/
├── output/             # Üretilen ses dosyaları
├── src/               
│   ├── tts_engine.py   # Ana TTS motoru
│   ├── gui.py         # Kullanıcı arayüzü
│   └── bot_integration.py # Bot entegrasyonu
├── models/            # İndirilen modeller (otomatik oluşur)
├── requirements.txt   # Bağımlılıklar
└── README.md         # Proje dokümantasyonu
```

## Ö<PERSON><PERSON><PERSON>
- [x] Sadece Türkçe ve İngilizce dil desteği (optimize)
- [x] Ses klonlama desteği
- [x] Basit ve kullanıcı dostu arayüz
- [x] Önceden tanımlı sesler
- [x] Yerel model yönetimi
- [x] Bot entegrasyonu
- [x] Taşınabilir yapı
- [x] Performans optimizasyonu

## Tamamlanan İşlemler
- [x] Proje planı oluşturuldu
- [x] Klasör yapısı oluşturuldu
- [x] TTS motoru geliştirildi
- [x] Bot entegrasyonu tamamlandı
- [x] GUI arayüzü eklendi
- [x] Dil desteği optimize edildi
- [x] Model yönetimi yerelleştirildi
- [x] Performans iyileştirmeleri yapıldı

## Kullanım
1. Bağımlılıkları yükleyin:
   ```bash
   pip install -r requirements.txt
   ```

2. GUI'yi başlatın:
   ```bash
   python src/gui.py
   ```

3. Bot entegrasyonu için:
   ```python
   from src.bot_integration import quick_speak
   
   # Türkçe
   audio_file = quick_speak("Merhaba dünya!", "tr")
   
   # İngilizce
   audio_file = quick_speak("Hello world!", "en")
   ```

## Notlar
- Modeller otomatik olarak `models/` klasörüne indirilir
- Ses dosyaları `output/` klasöründe oluşturulur
- Sadece Türkçe ve İngilizce dilleri desteklenir
- Varsayılan hoparlörler: Ana Florence, Claribel Dervla
- Ses klonlama için WAV formatı önerilir
- Tüm işlemler yerel olarak yapılır

## Kaynaklar
- Coqui TTS Dokümantasyonu: https://coqui-tts.readthedocs.io/
- PyQt5 Dokümantasyonu: https://www.riverbankcomputing.com/static/Docs/PyQt5/
- XTTS v2 Model Bilgisi: https://huggingface.co/coqui/XTTS-v2 