# 🐸 Yerel TTS Sistemi - Ses Klonlama ve Bot Entegrasyonu

Bu proje, **tamamen yerel çal<PERSON>**, **taşınabilir** ve **bot entegrasyonu** destekli bir Text-to-Speech (TTS) sistemidir. Coqui TTS tabanlı bu sistem, kendi sesinizi klonlayabilir ve diğer Python projelerinizde kolayca kullanabilirsiniz.

## ✨ Özellikler

- 🏠 **Tamamen Yerel**: Modeller proje klasöründe saklanır
- 🎭 **Ses Klonlama**: 10-30 saniyelik ses örnekleriyle kendi sesinizi klonlayın  
- 🌐 **Çok Dilli**: Türkçe ve İngilizce odaklı, 16 dil destekli
- 🤖 **Bot Entegrasyonu**: Tek satır kodla diğer projelerinizde kullanın
- 📦 **Taşınabilir**: Klasörü farklı bilgisayarlara taşıyabilirsiniz
- ⚡ **Hızlı**: GPU desteği ile optimize edilmiş performans
- 🔧 **Geliştirilebilir**: Açık kaynak ve modüler yapı

## 🚀 Hızlı Başlangıç

### 1. Kurulum
```bash
# Projeyi klonlayın veya indirin
git clone <repo-url>
cd "Rec TTS"

# Otomatik kurulum script'i çalıştırın
python setup_local_tts.py
```

### 2. İlk Test
```bash
# Temel TTS testini çalıştırın
python src/tts_engine.py

# Örnek kullanımları görün
python ornek_kullanim.py
```

### 3. Bot Entegrasyonu
```python
from src.bot_integration import quick_turkish

# Tek satırda TTS kullanın
audio_file = quick_turkish("Merhaba dünya!")
print(f"Ses dosyası: {audio_file}")
```

## 📁 Proje Yapısı

```
Rec TTS/
├── 📁 src/                    # Ana kaynak kodlar
│   ├── tts_engine.py         # TTS motoru
│   └── bot_integration.py    # Bot entegrasyonu API
├── 📁 models/                # AI modelleri (yerel)
├── 📁 output/                # Oluşturulan ses dosyaları
├── 📁 voice_samples/         # Ses klonlama örnekleri
│   └── 📁 your_voice/        # Kendi ses kayıtlarınız
├── 📄 setup_local_tts.py     # Otomatik kurulum
├── 📄 ornek_kullanim.py      # Kullanım örnekleri
├── 📄 bot_ornegi.py          # Bot entegrasyonu örneği
└── 📄 requirements.txt       # Python bağımlılıkları
```

## 🎤 Ses Klonlama

### Kendi Sesinizi Kaydedin
1. **Süre**: 10-30 saniye
2. **Format**: WAV dosyası (22050 Hz önerili)
3. **Ortam**: Sessiz, gürültüsüz
4. **İçerik**: Doğal konuşma, net telaffuz

### Örnek Kayıt Metni
```
"Merhaba, benim adım [ADINIZ]. Bu ses klonlama için yaptığım bir kayıt. 
Bugün hava güzel ve ben mutluyum. Bu teknoloji gerçekten etkileyici."
```

### Kullanım
```python
from src.bot_integration import SimpleTTS

tts = SimpleTTS()
audio = tts.clone_voice(
    "Bu benim klonlanmış sesim ile söylediğim test cümlesi.",
    "voice_samples/your_voice/my_voice.wav",
    "tr"
)
```

## 🤖 Bot Entegrasyonu

### Basit Kullanım
```python
from src.bot_integration import quick_turkish, quick_english

# Türkçe
ses_tr = quick_turkish("Bot için Türkçe mesaj")

# İngilizce  
audio_en = quick_english("English message for bot")
```

### Gelişmiş Bot Örneği
```python
from src.bot_integration import SimpleTTS

class MyBot:
    def __init__(self):
        self.tts = SimpleTTS()
    
    def respond(self, message):
        response = f"Mesajınız: {message}. Teşekkürler!"
        return self.tts.speak_turkish(response)

bot = MyBot()
audio = bot.respond("Merhaba!")
```

## 🌐 Desteklenen Diller

**Ana Odak**: Türkçe (tr), İngilizce (en)

**Diğer Desteklenen Diller**: 
- 🇪🇸 İspanyolca (es)
- 🇫🇷 Fransızca (fr) 
- 🇩🇪 Almanca (de)
- 🇮🇹 İtalyanca (it)
- 🇵🇹 Portekizce (pt)
- 🇵🇱 Lehçe (pl)
- 🇷🇺 Rusça (ru)
- 🇳🇱 Hollandaca (nl)
- 🇨🇿 Çekçe (cs)
- 🇸🇦 Arapça (ar)
- 🇨🇳 Çince (zh-cn)
- 🇯🇵 Japonca (ja)
- 🇭🇺 Macarca (hu)
- 🇰🇷 Korece (ko)

## ⚙️ Teknik Gereksinimler

- **Python**: 3.10-3.13
- **İşletim Sistemi**: Windows, Linux, MacOS
- **RAM**: Minimum 8GB (16GB önerili)
- **GPU**: CUDA destekli GPU (opsiyonel, hız için)
- **Disk**: ~2GB model dosyaları için

## 🔧 Geliştiriciler İçin

### API Referansı

#### LocalTTSEngine
```python
from src.tts_engine import LocalTTSEngine

tts = LocalTTSEngine()

# Önceden tanımlı ses
audio1 = tts.preset_voice_tts(text, speaker="Ana Florence", language="tr")

# Ses klonlama
audio2 = tts.clone_voice_tts(text, speaker_wav="ses.wav", language="tr")

# Hızlı TTS
audio3 = tts.quick_tts(text, voice_file="ses.wav")
```

#### SimpleTTS (Bot Entegrasyonu)
```python
from src.bot_integration import SimpleTTS

tts = SimpleTTS()

# Temel kullanım
audio = tts.speak(text, language="tr", voice_file="ses.wav")

# Dil kısayolları
audio_tr = tts.speak_turkish(text)
audio_en = tts.speak_english(text)

# Ses klonlama
audio_clone = tts.clone_voice(text, voice_sample="ses.wav", language="tr")
```

## 📦 Taşınabilirlik

Bu sistem tamamen taşınabilir şekilde tasarlanmıştır:

1. **Modeller yerel**: `models/` klasöründe saklanır
2. **Environment variable**: `TTS_HOME` otomatik ayarlanır
3. **Bağımsız çalışma**: İnternet bağlantısı sadece ilk kurulumda gerekli
4. **Klasör taşıma**: Tüm klasörü başka bilgisayara kopyalayabilirsiniz

## 🛠️ Sorun Giderme

### Model Indirme Sorunları
```bash
# TTS_HOME environment variable'ını manuel ayarlayın
export TTS_HOME="/path/to/your/project/models"  # Linux/Mac
set TTS_HOME=C:\path\to\your\project\models     # Windows
```

### Ses Kalitesi İyileştirme
- Kayıt kalitesini artırın (22050 Hz minimum)
- Arka plan gürültüsünü azaltın  
- Farklı duygusal tonlarla kayıt yapın
- Daha uzun ses örnekleri kullanın (15-30 saniye)

### Performans Optimizasyonu
- GPU kullanın (`CUDA_VISIBLE_DEVICES=0`)
- Batch processing için metinleri küçük parçalara bölün
- Model yüklemesi tek seferlik (singleton pattern)

## 📖 Daha Fazla Bilgi

- **Ses Klonlama Kılavuzu**: `voice_samples/SES_KLONLAMA_KILAVUZU.md`
- **Kullanım Örnekleri**: `ornek_kullanim.py`
- **Bot Entegrasyonu**: `bot_ornegi.py`
- **Coqui TTS Dokümanları**: [coqui-tts.readthedocs.io](https://coqui-tts.readthedocs.io)

## 🤝 Katkıda Bulunma

Bu proje açık kaynak ruhundadır. Katkılarınızı bekliyoruz:

1. Projeyi fork edin
2. Yeni özellik dalı oluşturun: `git checkout -b yeni-ozellik`
3. Değişiklerinizi commit edin: `git commit -m 'Yeni özellik'`
4. Dalınızı push edin: `git push origin yeni-ozellik`
5. Pull Request oluşturun

## 📄 Lisans

Bu proje açık kaynak lisansı altında dağıtılmaktadır. Detaylar için `LICENSE` dosyasına bakınız.

## 🙏 Teşekkürler

- [Coqui TTS](https://github.com/coqui-ai/TTS) - Temel TTS motoru
- [XTTS v2](https://docs.coqui.ai/en/latest/models/xtts.html) - Ses klonlama teknolojisi
- Açık kaynak topluluğu - Sürekli geliştirme ve destek

---

**🎯 Hedef**: Basit, güçlü ve herkesin kullanabileceği yerel TTS sistemi

**🔧 Geliştirici**: AI destekli ses teknolojileri meraklısı

**📧 İletişim**: Sorularınız için issue açabilirsiniz 