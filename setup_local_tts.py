#!/usr/bin/env python3
"""
Yerel TTS Kurulum ve Konfigürasyon Scripti
Bu script sistemi kurar ve kendi sesinizi klonlamanız için yardımcı olur.
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def print_header(text):
    """G<PERSON>zel başlık yazdır"""
    print("\n" + "=" * 60)
    print(f"🐸 {text}")
    print("=" * 60)

def print_step(step, text):
    """Adım yazdır"""
    print(f"\n📋 Adım {step}: {text}")

def check_python_version():
    """Python versiyonunu kontrol et"""
    print_step(1, "Python versiyonu kontrol ediliyor...")
    
    version = sys.version_info
    print(f"   Python versiyonu: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor < 10:
        print("❌ Python 3.10 veya üzeri gerekli!")
        print("   Lütfen Python'u güncelleyin: https://www.python.org/downloads/")
        return False
    
    print("✅ Python versiyonu uygun")
    return True

def install_requirements():
    """Gereksinimleri yükle"""
    print_step(2, "Bağımlılıklar yükleniyor...")
    
    try:
        # requirements.txt'yi kontrol et
        req_file = Path("requirements.txt")
        if not req_file.exists():
            print("❌ requirements.txt bulunamadı!")
            return False
        
        # pip install çalıştır
        cmd = [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"]
        print(f"   Konut: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Bağımlılıklar başarıyla yüklendi")
            return True
        else:
            print(f"❌ Yükleme hatası: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Yükleme hatası: {e}")
        return False

def create_directories():
    """Gerekli klasörleri oluştur"""
    print_step(3, "Klasör yapısı oluşturuluyor...")
    
    directories = [
        "models",
        "output", 
        "voice_samples",
        "voice_samples/your_voice",
        "voice_samples/examples"
    ]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"   📁 {dir_name}")
    
    print("✅ Klasör yapısı hazır")

def create_voice_sample_guide():
    """Ses örneği kılavuzu oluştur"""
    print_step(4, "Ses klonlama kılavuzu oluşturuluyor...")
    
    guide_content = """# 🎤 Ses Klonlama Kılavuzu

## Kendi Sesinizi Kaydetmek İçin:

### 1. Kayıt Gereksinimleri:
- **Süre**: 10-30 saniye arası
- **Format**: WAV dosyası (MP3 de çalışır ama WAV daha iyi)
- **Kalite**: 22050 Hz veya 44100 Hz
- **Ses seviyesi**: Orta seviye (çok yüksek veya çok düşük olmasın)
- **Ortam**: Sessiz ortam (arka plan gürültüsü olmasın)

### 2. Ne Söylemelisiniz:
```
"Merhaba, benim adım [ADINIZ]. Bu ses klonlama için yaptığım bir kayıt. 
Bugün hava güzel ve ben mutluyum. Bu teknoloji gerçekten etkileyici."
```

### 3. Kayıt İpuçları:
- Normal konuşma hızınızda konuşun
- Net ve açık telaffuz edin
- Duygulu konuşun (monoton olmasın)
- Kayıt sırasında hareket etmeyin

### 4. Kayıt Yerleştirme:
Kaydınızı şu klasöre koyun: `voice_samples/your_voice/my_voice.wav`

### 5. Test Etme:
```python
from src.bot_integration import SimpleTTS

tts = SimpleTTS()
audio = tts.clone_voice(
    "Bu benim klonlanmış sesim ile söylediğim test cümlesi.",
    "voice_samples/your_voice/my_voice.wav",
    "tr"
)
```

## 🎯 Kaliteli Sonuç İçin:
1. Birkaç farklı kayıt yapın ve en iyisini seçin
2. Kaydın başında ve sonunda sessizlik olmasın
3. Tek nefeste, doğal bir şekilde konuşun
4. Mikrofonunuz ağzınızdan 15-20 cm uzaklıkta olsun

## 🚀 İleri Seviye:
- Farklı duygularla (mutlu, ciddı, meraklı) kayıt yapabilirsiniz
- Çoklu dil desteği için her dilde ayrı kayıt yapın
- Uzun metinler için paragrafları bölerek ayrı ayrı sentezleyin
"""
    
    guide_file = Path("voice_samples/SES_KLONLAMA_KILAVUZU.md")
    guide_file.write_text(guide_content, encoding='utf-8')
    print(f"   📖 Kılavuz oluşturuldu: {guide_file}")
    print("✅ Ses klonlama kılavuzu hazır")

def create_example_usage():
    """Örnek kullanım dosyası oluştur"""
    print_step(5, "Örnek kullanım kodları oluşturuluyor...")
    
    example_content = '''#!/usr/bin/env python3
"""
🤖 TTS Kullanım Örnekleri
Bu dosyayı çalıştırarak farklı TTS özelliklerini test edebilirsiniz.
"""

import sys
from pathlib import Path

# Proje klasörünü path'e ekle
sys.path.append(str(Path(__file__).parent))

from src.bot_integration import SimpleTTS, quick_turkish, quick_english

def demo_basic_tts():
    """Temel TTS kullanımı"""
    print("\\n🔊 Demo 1: Temel TTS")
    print("-" * 30)
    
    tts = SimpleTTS()
    
    if tts.is_ready():
        # Türkçe test
        audio1 = tts.speak_turkish("Merhaba! Bu temel TTS kullanımı örneğidir.")
        print(f"✅ Türkçe ses oluşturuldu: {audio1}")
        
        # İngilizce test
        audio2 = tts.speak_english("Hello! This is a basic TTS usage example.")
        print(f"✅ English audio created: {audio2}")

def demo_voice_cloning():
    """Ses klonlama örneği"""
    print("\\n🎭 Demo 2: Ses Klonlama")
    print("-" * 30)
    
    tts = SimpleTTS()
    
    # Önce örnek ses oluştur
    print("Örnek ses oluşturuluyor...")
    sample_audio = tts.create_voice_sample("Merhaba, ben örnek ses dosyasıyım.")
    
    if sample_audio:
        print(f"✅ Örnek ses: {sample_audio}")
        
        # Bu ses ile klonlama yap
        cloned_audio = tts.clone_voice(
            "Bu klonlanmış ses ile söylenen yepyeni bir cümle.",
            sample_audio,
            "tr"
        )
        
        if cloned_audio:
            print(f"✅ Klonlanmış ses: {cloned_audio}")

def demo_bot_integration():
    """Bot entegrasyonu örneği"""
    print("\\n🤖 Demo 3: Bot Entegrasyonu")
    print("-" * 30)
    
    # Hızlı fonksiyonlar
    audio1 = quick_turkish("Bot entegrasyonu için hızlı Türkçe konuşma!")
    audio2 = quick_english("Quick English speech for bot integration!")
    
    print(f"✅ Hızlı Türkçe: {audio1}")
    print(f"✅ Quick English: {audio2}")

def demo_custom_voice():
    """Kendi sesinizle test (eğer kayıt varsa)"""
    print("\\n🎤 Demo 4: Kendi Sesiniz")
    print("-" * 30)
    
    voice_file = Path("voice_samples/your_voice/my_voice.wav")
    
    if voice_file.exists():
        print(f"Ses dosyası bulundu: {voice_file}")
        
        tts = SimpleTTS()
        cloned = tts.clone_voice(
            "Bu benim kendi sesim ile oluşturulmuş bir test cümlesi! Teknoloji gerçekten harika.",
            str(voice_file),
            "tr"
        )
        
        if cloned:
            print(f"✅ Kendi sesinizle: {cloned}")
    else:
        print("❌ Kendi ses dosyanız bulunamadı.")
        print(f"   Lütfen ses kaydınızı şuraya koyun: {voice_file}")
        print("   Kılavuz için: voice_samples/SES_KLONLAMA_KILAVUZU.md")

if __name__ == "__main__":
    print("🐸 TTS Kullanım Örnekleri")
    print("=" * 40)
    
    # Tüm demoları çalıştır
    demo_basic_tts()
    demo_voice_cloning() 
    demo_bot_integration()
    demo_custom_voice()
    
    print("\\n" + "=" * 40)
    print("✅ Tüm demolar tamamlandı!")
    print("📁 Ses dosyalarını 'output' klasöründe bulabilirsiniz.")
'''
    
    example_file = Path("ornek_kullanim.py")
    example_file.write_text(example_content, encoding='utf-8')
    print(f"   🐍 Örnek kodlar: {example_file}")
    print("✅ Örnek kullanım dosyası hazır")

def create_bot_example():
    """Bot entegrasyonu örneği oluştur"""
    print_step(6, "Bot entegrasyon örneği oluşturuluyor...")
    
    bot_example = '''#!/usr/bin/env python3
"""
🤖 Bot Entegrasyon Örneği
Bu örnek, TTS sisteminizi diğer Python projelerinizde nasıl kullanacağınızı gösterir.
"""

import sys
from pathlib import Path

# TTS modülünü import et
sys.path.append(str(Path(__file__).parent))

from src.bot_integration import quick_turkish, quick_english, SimpleTTS

class MyBot:
    """Örnek bot sınıfı"""
    
    def __init__(self):
        self.tts = SimpleTTS()
        self.my_voice_file = "voice_samples/your_voice/my_voice.wav"
    
    def say_welcome(self, user_name: str):
        """Karşılama mesajı"""
        message = f"Merhaba {user_name}! Hoş geldin, nasılsın?"
        
        # Kendi sesiniz varsa onu kullan
        if Path(self.my_voice_file).exists():
            return self.tts.clone_voice(message, self.my_voice_file, "tr")
        else:
            return self.tts.speak_turkish(message)
    
    def say_goodbye(self):
        """Veda mesajı"""
        return quick_turkish("Güle güle! Tekrar görüşmek üzere.")
    
    def translate_and_speak(self, turkish_text: str):
        """Türkçe metni İngilizce'ye çevir ve seslendir (basit örnek)"""
        # Bu gerçek bir çeviri değil, sadece örnek
        english_text = "This is an example English translation."
        
        # Her iki dilde de seslendir
        tr_audio = quick_turkish(turkish_text)
        en_audio = quick_english(english_text)
        
        return tr_audio, en_audio
    
    def respond_to_command(self, command: str):
        """Komutlara yanıt ver"""
        responses = {
            "saat": "Şu anda saat tam olarak bilemiyorum ama güzel bir gün!",
            "hava": "Hava durumu bilgim yok ama bugün güzel bir gün olduğunu hissediyorum.",
            "isim": "Ben yerel TTS sistemi ile çalışan örnek bir botum.",
            "çıkış": "Çıkış yapıyorum. Güle güle!"
        }
        
        response = responses.get(command.lower(), "Bu komutu anlayamadım. Tekrar söyler misin?")
        return self.tts.speak_turkish(response)

def simple_chat_example():
    """Basit sohbet örneği"""
    print("🤖 Basit Bot Sohbet Örneği")
    print("-" * 30)
    
    bot = MyBot()
    
    if not bot.tts.is_ready():
        print("❌ TTS sistemi hazır değil!")
        return
    
    # Karşılama
    welcome_audio = bot.say_welcome("Kullanıcı")
    print(f"🔊 Karşılama: {welcome_audio}")
    
    # Birkaç komut dene
    commands = ["saat", "hava", "isim"]
    
    for cmd in commands:
        print(f"\\n👤 Kullanıcı: {cmd}")
        response_audio = bot.respond_to_command(cmd)
        print(f"🤖 Bot: {response_audio}")
    
    # Veda
    goodbye_audio = bot.say_goodbye()
    print(f"🔊 Veda: {goodbye_audio}")

if __name__ == "__main__":
    simple_chat_example()
'''
    
    bot_file = Path("bot_ornegi.py")
    bot_file.write_text(bot_example, encoding='utf-8')
    print(f"   🤖 Bot örneği: {bot_file}")
    print("✅ Bot entegrasyon örneği hazır")

def test_installation():
    """Kurulumu test et"""
    print_step(7, "Kurulum test ediliyor...")
    
    try:
        # TTS modülünü import etmeye çalış
        from src.tts_engine import LocalTTSEngine
        print("✅ TTS modülü başarıyla import edildi")
        
        # Temel test
        print("   Model yükleniyor... (Bu biraz zaman alabilir)")
        tts = LocalTTSEngine()
        
        if tts:
            print("✅ TTS motoru başarıyla yüklendi")
            
            # Basit test
            test_audio = tts.preset_voice_tts(
                "Kurulum test mesajı. Sistem çalışıyor!",
                output_filename="kurulum_test.wav"
            )
            
            if test_audio:
                print(f"✅ Test ses dosyası oluşturuldu: {test_audio}")
                return True
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        return False
    
    return False

def main():
    """Ana kurulum fonksiyonu"""
    print_header("YERELtraining TTS SİSTEMİ KURULUMU")
    
    print("Bu script yerel TTS sisteminizi kuracak ve konfigüre edecek.")
    print("Kurulum birkaç dakika sürebilir çünkü AI modelleri indirilecek.")
    
    # Onay al
    response = input("\\nDevam etmek istiyor musunuz? (e/h): ").lower()
    if response not in ['e', 'evet', 'y', 'yes']:
        print("Kurulum iptal edildi.")
        return
    
    # Kurulum adımları
    steps = [
        check_python_version,
        install_requirements, 
        create_directories,
        create_voice_sample_guide,
        create_example_usage,
        create_bot_example,
        test_installation
    ]
    
    success = True
    for step_func in steps:
        if not step_func():
            success = False
            break
    
    # Sonuç
    print("\\n" + "=" * 60)
    if success:
        print("🎉 KURULUM BAŞARILI!")
        print("\\n📋 Sonraki adımlar:")
        print("   1. 'voice_samples/SES_KLONLAMA_KILAVUZU.md' dosyasını okuyun")
        print("   2. Kendi sesinizi kaydedin ve 'voice_samples/your_voice/' klasörüne koyun")
        print("   3. 'python ornek_kullanim.py' ile test edin")
        print("   4. 'python bot_ornegi.py' ile bot entegrasyonunu görün")
        print("\\n🚀 TTS sisteminiz kullanıma hazır!")
    else:
        print("❌ KURULUM BAŞARISIZ!")
        print("\\nLütfen hataları düzeltin ve tekrar deneyin.")
        print("Yardım için: https://github.com/coqui-ai/TTS")
    
    print("=" * 60)

if __name__ == "__main__":
    main() 