"""
Bot Entegrasyonu için Basit TTS API
Bu dosya diğer Python projelerinizde import edilerek kullanılabilir.
"""

import os
import sys
from pathlib import Path
from typing import Optional, Union

# TTS engine'i import et
try:
    from .tts_engine import LocalTTSEngine
except ImportError:
    # Eğer modül olarak çalışmıyorsa direkt import et
    sys.path.append(str(Path(__file__).parent))
    from tts_engine import LocalTTSEngine

class SimpleTTS:
    """Bot entegrasyonu için basitleştirilmiş TTS sınıfı"""
    
    def __init__(self, custom_models_dir: Optional[str] = None):
        """
        Basit TTS başlatıcı
        Args:
            custom_models_dir: İsteğe bağlı özel model klasörü
        """
        try:
            self.engine = LocalTTSEngine(custom_models_dir)
            self.initialized = True
            print("🤖 Bot TTS hazır!")
        except Exception as e:
            print(f"❌ TTS başlatma hatası: {e}")
            self.initialized = False
    
    def speak(self, text: str, language: str = "tr", voice_file: Optional[str] = None) -> Optional[str]:
        """
        Temel konuşma fonksiyonu
        Args:
            text: Konuşulacak metin
            language: Dil kodu (tr, en)  
            voice_file: Opsiyonel ses klonlama dosyası
        Returns:
            Oluşturulan ses dosyası yolu veya None
        """
        if not self.initialized:
            print("❌ TTS başlatılmamış!")
            return None
        
        try:
            return self.engine.quick_tts(text, voice_file, language)
        except Exception as e:
            print(f"❌ Konuşma hatası: {e}")
            return None
    
    def speak_turkish(self, text: str, voice_file: Optional[str] = None) -> Optional[str]:
        """Türkçe konuşma kısayolu"""
        return self.speak(text, "tr", voice_file)
    
    def speak_english(self, text: str, voice_file: Optional[str] = None) -> Optional[str]:
        """İngilizce konuşma kısayolu"""
        return self.speak(text, "en", voice_file)
    
    def clone_voice(self, text: str, voice_sample: Union[str, Path], language: str = "tr") -> Optional[str]:
        """
        Ses klonlama fonksiyonu
        Args:
            text: Metin
            voice_sample: Klonlanacak ses dosyası
            language: Dil
        """
        if not self.initialized:
            return None
        
        try:
            return self.engine.clone_voice_tts(text, voice_sample, language)
        except Exception as e:
            print(f"❌ Ses klonlama hatası: {e}")
            return None
    
    def get_supported_languages(self) -> list:
        """Desteklenen dilleri döndür"""
        return ["tr", "en", "es", "fr", "de", "it", "pt", "pl", "ru", "nl", "cs", "ar", "zh-cn", "ja", "hu", "ko"]
    
    def is_ready(self) -> bool:
        """TTS hazır mı kontrolü"""
        return self.initialized
    
    def create_voice_sample(self, text: str = "Merhaba, ben bot sesiyim. Bu kısa bir test kaydı.") -> Optional[str]:
        """Bot için örnek ses oluştur"""
        if not self.initialized:
            return None
        
        try:
            return self.engine.create_voice_sample(text, "bot_voice_sample.wav")
        except Exception as e:
            print(f"❌ Ses örneği oluşturma hatası: {e}")
            return None


# Global TTS instance - tek seferlik yükleme için
_global_tts = None

def get_tts_instance(custom_models_dir: Optional[str] = None) -> SimpleTTS:
    """Global TTS instance'ını al (singleton pattern)"""
    global _global_tts
    if _global_tts is None:
        _global_tts = SimpleTTS(custom_models_dir)
    return _global_tts

def quick_speak(text: str, language: str = "tr", voice_file: Optional[str] = None) -> Optional[str]:
    """
    Hızlı konuşma fonksiyonu - import edip direkt kullanabilirsiniz
    
    Örnek kullanım:
        from src.bot_integration import quick_speak
        audio_file = quick_speak("Merhaba dünya!")
    """
    tts = get_tts_instance()
    return tts.speak(text, language, voice_file)

def quick_turkish(text: str, voice_file: Optional[str] = None) -> Optional[str]:
    """Hızlı Türkçe konuşma"""
    return quick_speak(text, "tr", voice_file)

def quick_english(text: str, voice_file: Optional[str] = None) -> Optional[str]:
    """Hızlı İngilizce konuşma"""  
    return quick_speak(text, "en", voice_file)


if __name__ == "__main__":
    """Test ve demo"""
    print("🤖 Bot Integration Test")
    print("=" * 30)
    
    # Temel test
    tts_bot = SimpleTTS()
    
    if tts_bot.is_ready():
        print("✅ Bot TTS hazır")
        
        # Türkçe test
        audio1 = tts_bot.speak_turkish("Merhaba! Ben bot entegrasyonu için geliştirilmiş basit TTS sistemi.")
        if audio1:
            print(f"🎵 Türkçe ses: {audio1}")
        
        # İngilizce test  
        audio2 = tts_bot.speak_english("Hello! I am a simple TTS system developed for bot integration.")
        if audio2:
            print(f"🎵 English audio: {audio2}")
        
        # Hızlı fonksiyonlar testi
        audio3 = quick_turkish("Bu hızlı fonksiyon testi!")
        if audio3:
            print(f"🎵 Hızlı Türkçe: {audio3}")
        
        print("✅ Bot entegrasyonu test tamamlandı!")
    else:
        print("❌ Bot TTS başlatılamadı") 