import sys
import os
import shutil
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QTextEdit, QComboBox, 
                           QLabel, QFileDialog, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon, QPalette, QColor, QFont, QPixmap
from tts_engine import LocalTTSEngine
import win32gui
import win32con
import win32api
import torch
import gc

def set_app_icon():
    """Uygulama ikonunu ayarla"""
    try:
        # Windows için AppUserModelID
        import ctypes
        myappid = u'reclast.tts.desktop.v1'
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        
        # İkon dosyalarını kontrol et
        icon_path = Path(__file__).parent.parent / "assets" / "logo.png"
        ico_path = Path(__file__).parent.parent / "assets" / "logo.ico"
        
        app = QApplication.instance()
        if app:
            if ico_path.exists():
                # Windows API ile icon ayarla
                try:
                    hwnd = win32gui.GetForegroundWindow()
                    large_icon = win32gui.LoadImage(
                        0, str(ico_path), win32con.IMAGE_ICON,
                        win32gui.GetSystemMetrics(win32con.SM_CXICON),
                        win32gui.GetSystemMetrics(win32con.SM_CYICON),
                        win32con.LR_LOADFROMFILE
                    )
                    small_icon = win32gui.LoadImage(
                        0, str(ico_path), win32con.IMAGE_ICON,
                        win32gui.GetSystemMetrics(win32con.SM_CXSMICON),
                        win32gui.GetSystemMetrics(win32con.SM_CYSMICON),
                        win32con.LR_LOADFROMFILE
                    )
                    win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_BIG, large_icon)
                    win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_SMALL, small_icon)
                except:
                    pass
                
                app.setWindowIcon(QIcon(str(ico_path)))
            elif icon_path.exists():
                app.setWindowIcon(QIcon(str(icon_path)))
    except:
        pass

class TTSWorker(QThread):
    """Arka plan TTS işlemi için worker thread"""
    finished = pyqtSignal(str)  # Başarılı
    error = pyqtSignal(str)     # Hata
    progress = pyqtSignal(int)  # İlerleme
    
    def __init__(self, tts, text, language, voice_file=None, speaker=None):
        super().__init__()
        self.tts = tts
        self.text = text
        self.language = language
        self.voice_file = voice_file
        self.speaker = speaker
        
    def run(self):
        try:
            self.progress.emit(10)
            
            if self.voice_file:
                output = self.tts.clone_voice_tts(
                    text=self.text,
                    speaker_wav=self.voice_file,
                    language=self.language
                )
            else:
                output = self.tts.preset_voice_tts(
                    text=self.text,
                    speaker=self.speaker,
                    language=self.language
                )
                
            self.progress.emit(100)
            
            if output:
                self.finished.emit(str(output))
            else:
                self.error.emit("Ses oluşturulamadı!")
                
        except Exception as e:
            self.error.emit(str(e))

class ModernButton(QPushButton):
    """Modern stil buton"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setMinimumHeight(40)
        self.setFont(QFont('Segoe UI', 10))
        self.setCursor(Qt.PointingHandCursor)
        
        # Stil
        self.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                border: none;
                border-radius: 5px;
                color: white;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #95a5a6;
            }
        """)

class ModernComboBox(QComboBox):
    """Modern stil combo box"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.setMinimumHeight(40)
        self.setFont(QFont('Segoe UI', 10))
        
        # Stil
        self.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 5px;
                background: white;
                color: #2c3e50;
            }
            QComboBox:hover {
                border-color: #3498db;
            }
            QComboBox:drop-down {
                border: none;
                padding-right: 10px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #34495e;
                margin-right: 10px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background: white;
                color: #2c3e50;
                selection-background-color: #3498db;
                selection-color: white;
            }
        """)

class TTSGui(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RecLast TTS")
        self.setMinimumSize(800, 600)
        
        # İkon ayarla
        icon_path = Path(__file__).parent.parent / "assets" / "logo.png"
        ico_path = Path(__file__).parent.parent / "assets" / "logo.ico"
        
        if ico_path.exists():
            self.setWindowIcon(QIcon(str(ico_path)))
            # Windows API ile icon ayarla
            try:
                hwnd = self.winId().__int__()
                large_icon = win32gui.LoadImage(
                    0, str(ico_path), win32con.IMAGE_ICON,
                    win32gui.GetSystemMetrics(win32con.SM_CXICON),
                    win32gui.GetSystemMetrics(win32con.SM_CYICON),
                    win32con.LR_LOADFROMFILE
                )
                small_icon = win32gui.LoadImage(
                    0, str(ico_path), win32con.IMAGE_ICON,
                    win32gui.GetSystemMetrics(win32con.SM_CXSMICON),
                    win32gui.GetSystemMetrics(win32con.SM_CYSMICON),
                    win32con.LR_LOADFROMFILE
                )
                win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_BIG, large_icon)
                win32gui.SendMessage(hwnd, win32con.WM_SETICON, win32con.ICON_SMALL, small_icon)
            except:
                pass
        elif icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
        
        # TTS motorunu başlat
        try:
            self.tts = LocalTTSEngine()
            self.available_speakers = self.tts.list_available_speakers()
            
            # XTTS modelini kontrol et
            if not hasattr(self.tts, 'tts') or not self.tts.tts:
                raise Exception("XTTS modeli yüklenemedi!")
                
        except Exception as e:
            QMessageBox.critical(self, "Hata", f"TTS motoru başlatılamadı: {e}")
            sys.exit(1)
            
        # Ana widget ve layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Logo
        if icon_path.exists():
            logo_label = QLabel()
            logo_label.setPixmap(QIcon(str(icon_path)).pixmap(128, 128))
            logo_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(logo_label)
        
        # Dil seçimi
        lang_layout = QHBoxLayout()
        lang_label = QLabel("Dil:")
        lang_label.setFont(QFont('Segoe UI', 10))
        self.lang_combo = ModernComboBox()
        self.lang_combo.addItems(["Türkçe", "English"])
        lang_layout.addWidget(lang_label)
        lang_layout.addWidget(self.lang_combo)
        lang_layout.addStretch()
        layout.addLayout(lang_layout)
        
        # Ses seçimi
        voice_layout = QHBoxLayout()
        voice_label = QLabel("Ses:")
        voice_label.setFont(QFont('Segoe UI', 10))
        self.voice_combo = ModernComboBox()
        
        # Türkçe ve İngilizce sesleri ayır
        self.tr_voices = ["Ana Florence"]  # Sadece varsayılan Türkçe ses
        self.en_voices = [name for name in self.available_speakers if name != "Ana Florence"]  # Diğer tüm sesler
        
        # Ses listesini oluştur (Klonlama ve Varsayılan en üstte)
        self.voice_combo.addItem("Ses Klonlama")
        self.voice_combo.addItem("Varsayılan Ses")
        self.update_voice_list("Türkçe")  # Başlangıçta Türkçe sesleri göster
        
        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_combo)
        
        # Ses dosyası seçimi butonu
        self.select_voice_btn = QPushButton("Ses Dosyası Seç")
        self.select_voice_btn.setMinimumHeight(40)
        self.select_voice_btn.setFont(QFont('Segoe UI', 10))
        self.select_voice_btn.setCursor(Qt.PointingHandCursor)
        self.select_voice_btn.setVisible(False)  # Başlangıçta gizli
        voice_layout.addWidget(self.select_voice_btn)
        voice_layout.addStretch()
        layout.addLayout(voice_layout)
        
        # Metin girişi
        text_label = QLabel("Metin:")
        text_label.setFont(QFont('Segoe UI', 10))
        layout.addWidget(text_label)
        
        self.text_edit = QTextEdit()
        self.text_edit.setFont(QFont('Segoe UI', 11))
        self.text_edit.setMinimumHeight(200)
        self.text_edit.setPlaceholderText("Buraya metninizi yazın...")
        self.text_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                background: white;
                color: #2c3e50;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        layout.addWidget(self.text_edit)
        
        # İlerleme çubuğu
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                color: #2c3e50;
            }
            QProgressBar::chunk {
                background-color: #2ecc71;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # Kontrol butonları
        btn_layout = QHBoxLayout()
        self.generate_btn = ModernButton("Sesi Oluştur")
        self.play_btn = ModernButton("Son Sesi Çal")
        self.play_btn.setEnabled(False)
        btn_layout.addWidget(self.generate_btn)
        btn_layout.addWidget(self.play_btn)
        layout.addLayout(btn_layout)
        
        # Durum çubuğu
        self.statusBar().setFont(QFont('Segoe UI', 9))
        self.statusBar().showMessage("Hazır")
        
        # Sinyalleri bağla
        self.voice_combo.currentTextChanged.connect(self.on_voice_changed)
        self.lang_combo.currentTextChanged.connect(self.on_language_changed)
        self.select_voice_btn.clicked.connect(self.select_voice_file)
        self.generate_btn.clicked.connect(self.generate_audio)
        self.play_btn.clicked.connect(self.play_audio)
        
        # Değişkenler
        self.voice_file = None
        self.last_audio = None
        self.worker = None
        
        # Arkaplan rengi
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f6fa;
            }
        """)
        
        # Başlangıçta ses seçimi durumunu kontrol et
        self.on_voice_changed(self.voice_combo.currentText())
        
    def update_voice_list(self, language):
        """Dile göre ses listesini güncelle"""
        current_text = self.voice_combo.currentText()
        self.voice_combo.clear()
        
        # Klonlama ve Varsayılan her zaman en üstte
        self.voice_combo.addItem("Ses Klonlama")
        self.voice_combo.addItem("Varsayılan Ses")
        
        # Dile göre sesleri ekle
        if language == "Türkçe":
            self.voice_combo.addItems(self.tr_voices)
        else:
            self.voice_combo.addItems(self.en_voices)
            
        # Önceki seçimi koru (eğer hala mevcutsa)
        index = self.voice_combo.findText(current_text)
        if index >= 0:
            self.voice_combo.setCurrentIndex(index)
        else:
            self.voice_combo.setCurrentText("Varsayılan Ses")
            
    def on_language_changed(self, language):
        """Dil değiştiğinde sesleri güncelle"""
        self.update_voice_list(language)
        
    def on_voice_changed(self, text):
        """Ses seçimi değiştiğinde"""
        is_clone = text == "Ses Klonlama"
        self.select_voice_btn.setEnabled(is_clone)
        self.select_voice_btn.setVisible(is_clone)  # Butonu göster/gizle
        
        if is_clone:
            self.select_voice_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    border: none;
                    border-radius: 5px;
                    color: white;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #2573a7;
                }
                QPushButton:enabled {
                    background-color: #3498db;
                    color: white;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                    color: #95a5a6;
                }
            """)
            self.select_voice_btn.setCursor(Qt.PointingHandCursor)
        else:
            self.voice_file = None  # Ses dosyasını temizle
        
    def select_voice_file(self):
        """Ses dosyası seç"""
        file, _ = QFileDialog.getOpenFileName(
            self,
            "Ses Dosyası Seç",
            "",
            "Ses Dosyaları (*.wav *.mp3);;Tüm Dosyalar (*.*)"
        )
        if file:
            # Assets klasörüne kopyala
            assets_dir = Path(__file__).parent.parent / "assets"
            assets_dir.mkdir(exist_ok=True)
            
            target_file = assets_dir / "clone_voice.wav"
            shutil.copy2(file, target_file)
            
            self.voice_file = str(target_file)
            self.statusBar().showMessage(f"Seçilen ses: {Path(file).name}")
            
    def generate_audio(self):
        """Ses oluştur"""
        text = self.text_edit.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "Uyarı", "Lütfen bir metin girin!")
            return
            
        # Ses klonlama kontrolü
        voice_selection = self.voice_combo.currentText()
        if voice_selection == "Ses Klonlama" and not self.voice_file:
            QMessageBox.warning(self, "Uyarı", "Lütfen önce bir ses dosyası seçin!")
            return
            
        # UI'yi devre dışı bırak
        self.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage("Ses oluşturuluyor...")
        
        try:
            # Dil kodunu belirle
            language = "tr" if self.lang_combo.currentText() == "Türkçe" else "en"
            
            # Worker thread'i başlat
            self.worker = TTSWorker(
                self.tts,
                text,
                language,
                self.voice_file if voice_selection == "Ses Klonlama" else None,
                voice_selection if voice_selection != "Varsayılan Ses" else "Ana Florence"
            )
            
            self.worker.finished.connect(self.on_generation_complete)
            self.worker.error.connect(self.on_generation_error)
            self.worker.progress.connect(self.progress_bar.setValue)
            
            self.worker.start()
                
        except Exception as e:
            self.on_generation_error(str(e))
            
    def on_generation_complete(self, output_file):
        """Ses oluşturma tamamlandığında"""
        self.last_audio = output_file
        self.statusBar().showMessage(f"Ses oluşturuldu: {Path(output_file).name}")
        self.play_btn.setEnabled(True)
        self.setEnabled(True)
        self.progress_bar.setVisible(False)
        
    def on_generation_error(self, error_msg):
        """Ses oluşturma hatası durumunda"""
        self.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # CUDA hatalarını daha anlaşılır hale getir
        if "CUDA error: device-side assert triggered" in error_msg:
            error_msg = "GPU hatası: Ses klonlama işlemi başarısız oldu. Lütfen şunları deneyin:\n\n" + \
                       "1. Daha kısa bir metin girin\n" + \
                       "2. Farklı bir ses dosyası kullanın\n" + \
                       "3. Uygulamayı yeniden başlatın"
        elif "illegal memory access" in error_msg:
            error_msg = "GPU bellek hatası: Yetersiz GPU belleği. Lütfen şunları deneyin:\n\n" + \
                       "1. Diğer uygulamaları kapatın\n" + \
                       "2. Daha kısa bir metin girin\n" + \
                       "3. Uygulamayı yeniden başlatın"
        elif "Input type" in error_msg and "weight type" in error_msg:
            error_msg = "GPU tensor tipi hatası: Lütfen uygulamayı yeniden başlatın."
            
        QMessageBox.critical(self, "Hata", error_msg)
        self.statusBar().showMessage("Hata oluştu!")
        
        # GPU'yu temizlemeye çalış
        if hasattr(self, 'tts') and self.tts:
            try:
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    gc.collect()
            except:
                pass
            
    def play_audio(self):
        """Son oluşturulan sesi çal"""
        if self.last_audio and os.path.exists(self.last_audio):
            if sys.platform == "win32":
                os.startfile(self.last_audio)
            else:
                import subprocess
                opener = "open" if sys.platform == "darwin" else "xdg-open"
                subprocess.call([opener, self.last_audio])
        else:
            QMessageBox.warning(self, "Uyarı", "Çalınacak ses bulunamadı!")

def main():
    # Yüksek DPI desteği
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    app = QApplication(sys.argv)
    
    # Uygulama ikonunu ayarla
    set_app_icon()
    
    window = TTSGui()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 