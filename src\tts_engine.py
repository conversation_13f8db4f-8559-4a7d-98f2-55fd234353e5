import os
import tempfile
from pathlib import Path
from typing import Optional, Union
from TTS.api import TTS
import torch
import gc

class LocalTTSEngine:
    def __init__(self, models_dir: Optional[str] = None):
        """
        Yerel TTS motoru - Modeller proje klasöründe saklanır
        Args:
            models_dir: <PERSON><PERSON><PERSON> (None ise project/models kullanılır)
        """
        # CUDA hata ayıklama için
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        os.environ['TORCH_USE_CUDA_DSA'] = '1'
        
        # PyTorch ayarları
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.enabled = True
        
        # Bellek temizliği
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gc.collect()
        
        self.project_root = Path(__file__).parent.parent
        self.output_dir = self.project_root / "output"
        
        # Modeller için <PERSON>
        if models_dir is None:
            self.models_dir = self.project_root / "models"
        else:
            self.models_dir = Path(models_dir)
        
        # Klasörleri oluştur
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        
        # TTS_HOME environment variable'ını ayarla
        os.environ['TTS_HOME'] = str(self.models_dir)
        
        print(f"✅ TTS_HOME ayarlandı: {self.models_dir}")
        print(f"✅ Çıktı klasörü: {self.output_dir}")
        
        # GPU kontrolü ve optimizasyonları
        if torch.cuda.is_available():
            self.device = "cuda"
            # GPU bilgisi
            gpu_name = torch.cuda.get_device_name()
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
            print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            self.device = "cpu"
            print("⚠️ GPU bulunamadı, CPU kullanılıyor")
        
        # Basit Türkçe-İngilizce model yükle
        print("🔄 XTTS v2 modeli yükleniyor...")
        
        try:
            # XTTS v2 modelini başlat
            self.tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2", progress_bar=True)
            
            # CUDA optimizasyonları
            if self.device == "cuda":
                # Model GPU'ya taşı
                self.tts = self.tts.to(self.device)
                
                # Mixed precision ayarları
                if hasattr(self.tts.synthesizer.tts_model, 'half'):
                    with torch.no_grad():
                        self.tts.synthesizer.tts_model = self.tts.synthesizer.tts_model.half()
                        for param in self.tts.synthesizer.tts_model.parameters():
                            param.data = param.data.half()
                
                # CUDA önbellekleme optimizasyonları
                torch.backends.cuda.max_split_size_mb = 512  # RTX 4060 8GB için optimize edildi
            
            # Mevcut hoparlörleri kontrol et
            if hasattr(self.tts.synthesizer.tts_model, 'speaker_manager') and self.tts.synthesizer.tts_model.speaker_manager:
                self.available_speakers = self.tts.synthesizer.tts_model.speaker_manager.speaker_names
                # Türkçe konuşabilen sesleri filtrele
                self.tr_speakers = ["Ana Florence"]  # Varsayılan Türkçe ses
                print(f"✅ Kullanılabilir hoparlörler: {len(self.available_speakers)}")
                print(f"🇹🇷 Türkçe hoparlörler: {len(self.tr_speakers)}")
            else:
                self.available_speakers = ["Ana Florence"]
                self.tr_speakers = ["Ana Florence"]
                print("✅ XTTS v2 modeli hazır (voice cloning destekli)")
                
        except Exception as e:
            print(f"❌ Model yükleme hatası: {e}")
            raise
    
    def optimize_model(self):
        """Model optimizasyonları"""
        if self.device == "cuda":
            # Bellek temizliği
            torch.cuda.empty_cache()
            gc.collect()
            
            # JIT optimizasyonu
            if hasattr(self.tts.synthesizer.tts_model, 'jit'):
                self.tts.synthesizer.tts_model = torch.jit.script(self.tts.synthesizer.tts_model)
    
    def list_available_speakers(self):
        """Kullanılabilir hoparlörleri listele"""
        return self.available_speakers if self.available_speakers else []
    
    def create_voice_sample(self, text: str = "Merhaba, bu benim sesim. Test için kısa bir kayıt yapıyorum.", filename: str = "voice_sample.wav"):
        """Ses klonlama için örnek ses dosyası oluştur"""
        output_path = self.output_dir / filename
        
        try:
            # GPU optimizasyonları
            if self.device == "cuda":
                torch.cuda.empty_cache()
                gc.collect()
                
                # CUDA bellek optimizasyonları
                torch.backends.cuda.max_split_size_mb = 128
                torch.backends.cudnn.benchmark = True
                
                # Gradyan hesaplamayı devre dışı bırak
                with torch.no_grad():
                    # Mixed precision ile ses oluştur
                    with torch.amp.autocast('cuda'):
                        # Giriş verilerini FP16'ya çevir
                        if hasattr(self.tts.synthesizer.tts_model, 'half'):
                            self.tts.synthesizer.tts_model = self.tts.synthesizer.tts_model.half()
                        
                        # Ses oluştur
                        self.tts.tts_to_file(
                            text=text,
                            speaker="Ana Florence",  # Varsayılan hoparlör
                            language="tr",
                            file_path=str(output_path)
                        )
            else:
                # CPU için normal işlem
                self.tts.tts_to_file(
                    text=text,
                    speaker="Ana Florence",  # Varsayılan hoparlör
                    language="tr",
                    file_path=str(output_path)
                )
            
            print(f"✅ Ses örneği oluşturuldu: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"❌ Ses örneği oluşturma hatası: {e}")
            return None
    
    def clone_voice_tts(self, text: str, speaker_wav: Union[str, Path], language: str = "tr", output_filename: Optional[str] = None):
        """
        Ses klonlama ile TTS
        Args:
            text: Sentezlenecek metin
            speaker_wav: Klonlanacak ses dosyası
            language: Dil kodu (tr, en)
            output_filename: Çıktı dosya adı
        """
        if not output_filename:
            output_filename = f"cloned_{language}_{hash(text)}.wav"
        
        output_path = self.output_dir / output_filename
        
        try:
            # Ses dosyasını kontrol et
            if not os.path.exists(speaker_wav):
                raise FileNotFoundError(f"Ses dosyası bulunamadı: {speaker_wav}")
            
            # GPU optimizasyonları
            if self.device == "cuda":
                # Bellek temizliği
                torch.cuda.empty_cache()
                gc.collect()
                
                # CUDA bellek optimizasyonları
                torch.backends.cuda.max_split_size_mb = 512
                torch.backends.cudnn.benchmark = True
                
                # Gradyan hesaplamayı devre dışı bırak
                with torch.no_grad():
                    # Mixed precision ile ses oluştur
                    with torch.amp.autocast('cuda', dtype=torch.float16):
                        try:
                            # Ses oluştur (gpt_cond_len ile)
                            self.tts.tts_to_file(
                                text=text,
                                speaker_wav=str(speaker_wav),
                                language=language,
                                file_path=str(output_path),
                                gpt_cond_len=3,  # Daha kısa conditioning length
                                use_deterministic_seed=True,  # Deterministik üretim
                                repetition_penalty=5.0,  # Tekrar cezası
                                temperature=0.75  # Sıcaklık değeri
                            )
                            
                            print(f"✅ Ses klonlandı: {output_path}")
                            return output_path
                            
                        except RuntimeError as e:
                            if "out of memory" in str(e):
                                print("❌ GPU bellek yetersiz! Daha kısa metin deneyin.")
                                raise
                            elif "device-side assert" in str(e):
                                print("❌ CUDA hatası! Ses dosyasını kontrol edin.")
                                raise
                            else:
                                raise
            else:
                # CPU için normal işlem
                self.tts.tts_to_file(
                    text=text,
                    speaker_wav=str(speaker_wav),
                    language=language,
                    file_path=str(output_path)
                )
            
            print(f"✅ Ses klonlandı: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"❌ Ses klonlama hatası: {e}")
            raise
    
    def preset_voice_tts(self, text: str, speaker: str = "Ana Florence", language: str = "tr", output_filename: Optional[str] = None):
        """
        Önceden tanımlı hoparlör ile TTS
        Args:
            text: Sentezlenecek metin  
            speaker: Hoparlör adı
            language: Dil kodu
            output_filename: Çıktı dosya adı
        """
        if not output_filename:
            output_filename = f"preset_{language}_{hash(text)}.wav"
        
        output_path = self.output_dir / output_filename
        
        try:
            # GPU optimizasyonları
            if self.device == "cuda":
                torch.cuda.empty_cache()
                gc.collect()
                
                # CUDA bellek optimizasyonları
                torch.backends.cuda.max_split_size_mb = 128
                torch.backends.cudnn.benchmark = True
                
                # Gradyan hesaplamayı devre dışı bırak
                with torch.no_grad():
                    # Mixed precision ile ses oluştur
                    with torch.amp.autocast('cuda'):
                        # Giriş verilerini FP16'ya çevir
                        if hasattr(self.tts.synthesizer.tts_model, 'half'):
                            self.tts.synthesizer.tts_model = self.tts.synthesizer.tts_model.half()
                        
                        # Ses oluştur
                        self.tts.tts_to_file(
                            text=text,
                            speaker=speaker,
                            language=language,
                            file_path=str(output_path)
                        )
            else:
                # CPU için normal işlem
                self.tts.tts_to_file(
                    text=text,
                    speaker=speaker,
                    language=language,
                    file_path=str(output_path)
                )
            
            print(f"✅ Ses oluşturuldu: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"❌ Ses oluşturma hatası: {e}")
            raise
    
    def quick_tts(self, text: str, voice_file: Optional[str] = None, language: str = "tr", speaker: str = "Ana Florence"):
        """
        Bot entegrasyonu için hızlı TTS fonksiyonu
        Args:
            text: Metín
            voice_file: Ses klonlama dosyası (opsiyonel)
            language: Dil
            speaker: Varsayılan hoparlör
        """
        # Kısa dosya adı oluştur
        filename = f"quick_{hash(text)}.wav"
        
        if voice_file and os.path.exists(voice_file):
            return self.clone_voice_tts(text, voice_file, language, filename)
        else:
            return self.preset_voice_tts(text, speaker, language, filename)
    
    def get_model_info(self):
        """Model bilgilerini döndür"""
        gpu_info = None
        if self.device == "cuda":
            gpu_name = torch.cuda.get_device_name()
            total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            allocated_memory = torch.cuda.memory_allocated() / 1024**3
            gpu_info = {
                "name": gpu_name,
                "total_memory": f"{total_memory:.1f}GB",
                "allocated_memory": f"{allocated_memory:.1f}GB"
            }
            
        return {
            "models_directory": str(self.models_dir),
            "output_directory": str(self.output_dir), 
            "device": self.device,
            "gpu_info": gpu_info,
            "available_speakers": self.list_available_speakers(),
            "supported_languages": ["tr", "en"]  # Sadece Türkçe ve İngilizce
        }

class nullcontext:
    """Context manager that does nothing"""
    def __enter__(self): return None
    def __exit__(self, *excinfo): pass

def main():
    """Test ve demo fonksiyonu"""
    print("=" * 50)
    print("🐸 Yerel TTS Engine Test Başlatılıyor...")
    print("=" * 50)
    
    try:
        # TTS engine'i başlat
        tts = LocalTTSEngine()
        
        # Model bilgilerini göster
        info = tts.get_model_info()
        print(f"📁 Modeller: {info['models_directory']}")
        print(f"📁 Çıktılar: {info['output_directory']}")
        print(f"🖥️  Cihaz: {info['device']}")
        
        if info['gpu_info']:
            print(f"🎮 GPU: {info['gpu_info']['name']}")
            print(f"📊 Toplam Bellek: {info['gpu_info']['total_memory']}")
            print(f"📊 Kullanılan Bellek: {info['gpu_info']['allocated_memory']}")
        
        print("=" * 50)
        print("🔄 Ses klonlama testi başlıyor...")
        
        # Klonlama için örnek ses dosyası
        voice_sample = Path(__file__).parent.parent / "assets" / "clone_voice.wav"
        print(f"📁 Ses dosyası: {voice_sample}")
        
        if not voice_sample.exists():
            raise FileNotFoundError(f"Ses dosyası bulunamadı: {voice_sample}")
        
        # 1. Kısa metin ile test
        print("\n🔤 Test 1: Kısa metin ile klonlama...")
        output1 = tts.clone_voice_tts(
            text="Merhaba, bu kısa bir test cümlesidir.",
            speaker_wav=str(voice_sample),
            language="tr",
            output_filename="test1_short.wav"
        )
        if output1:
            print(f"✅ Test 1 başarılı: {output1}")
            
        # GPU belleğini temizle
        if tts.device == "cuda":
            torch.cuda.empty_cache()
            gc.collect()
            
        # 2. Orta uzunlukta metin
        print("\n🔤 Test 2: Orta uzunlukta metin ile klonlama...")
        output2 = tts.clone_voice_tts(
            text="Bu biraz daha uzun bir test metnidir. Ses klonlama sisteminin farklı uzunluktaki metinlerle nasıl çalıştığını test ediyoruz.",
            speaker_wav=str(voice_sample),
            language="tr",
            output_filename="test2_medium.wav"
        )
        if output2:
            print(f"✅ Test 2 başarılı: {output2}")
            
        # GPU belleğini temizle
        if tts.device == "cuda":
            torch.cuda.empty_cache()
            gc.collect()
            
        # 3. Varsayılan ses ile test
        print("\n🔤 Test 3: Varsayılan ses ile test...")
        output3 = tts.preset_voice_tts(
            text="Bu test varsayılan ses ile yapılmaktadır.",
            language="tr",
            output_filename="test3_preset.wav"
        )
        if output3:
            print(f"✅ Test 3 başarılı: {output3}")
        
        print("\n" + "=" * 50)
        print("✅ Tüm testler tamamlandı!")
        print(f"📁 Ses dosyaları: {tts.output_dir}")
        print("=" * 50)
        
    except Exception as e:
        print(f"\n❌ Test hatası: {str(e)}")
        print("\nHata detayları:")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 